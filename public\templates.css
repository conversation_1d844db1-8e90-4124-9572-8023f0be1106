/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

/* Container */
.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.header h1 {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.nav-link {
  display: inline-block;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.nav-link:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
  transform: translateY(-2px);
}

/* Status messages */
.status-message {
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  font-weight: 500;
  display: none;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-message.success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border-left: 4px solid #28a745;
}

.status-message.error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.status-message.info {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

/* Form container */
.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
  min-width: 250px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-input::placeholder {
  color: #8899a6;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

/* Template list container */
.template-list-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 300px;
}

.list-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.template-list {
  display: grid;
  gap: 15px;
}

.template-item {
  background: white;
  border: 2px solid #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.3s ease forwards;
}

.template-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.template-item:hover::before {
  opacity: 1;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
}

.template-info {
  flex: 1;
  margin-right: 15px;
}

.template-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.template-content {
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-word;
}

.template-id {
  font-size: 12px;
  color: #adb5bd;
  font-family: 'Courier New', monospace;
}

.template-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.btn-small {
  padding: 8px 12px;
  font-size: 12px;
  min-width: auto;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #495057;
}

.empty-state p {
  font-size: 1rem;
  line-height: 1.5;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-item:nth-child(1) { animation-delay: 0.1s; }
.template-item:nth-child(2) { animation-delay: 0.2s; }
.template-item:nth-child(3) { animation-delay: 0.3s; }
.template-item:nth-child(4) { animation-delay: 0.4s; }
.template-item:nth-child(5) { animation-delay: 0.5s; }

/* Loading animation */
.loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-group {
    min-width: auto;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
  
  .template-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .template-info {
    margin-right: 0;
  }
  
  .template-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .header,
  .form-container,
  .template-list-container {
    padding: 15px;
  }

  .template-item {
    padding: 15px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .header,
  .form-container,
  .template-list-container {
    background: rgba(52, 73, 94, 0.95);
    color: #ecf0f1;
  }

  .template-item {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }

  .template-name {
    color: #ecf0f1;
  }

  .form-input {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }

  .form-input::placeholder {
    color: #95a5a6;
  }

  .form-label {
    color: #bdc3c7;
  }
}

/* Improved focus and hover states */
.form-input:hover {
  border-color: #c3d0e0;
}

.template-item:hover .template-name {
  color: #667eea;
}

/* Character counter for inputs */
.form-group {
  position: relative;
}

.char-counter {
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: 12px;
  color: #8899a6;
}

.char-counter.warning {
  color: #f39c12;
}

.char-counter.danger {
  color: #e74c3c;
}

/* Improved button states */
.btn:active {
  transform: translateY(0);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #a8b3c5 0%, #1a252f 100%);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d84315 100%);
}

.btn-success:hover {
  background: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
}

/* Tooltip styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Scrollbar styling */
.template-list::-webkit-scrollbar {
  width: 8px;
}

.template-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.template-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

.template-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* File Upload Styles */
.file-upload-area {
  margin-top: 10px;
}

.upload-zone {
  border: 2px dashed #e1e8ed;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.upload-zone:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.upload-zone.dragover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.02);
}

.upload-icon {
  font-size: 3rem;
  color: #8899a6;
  margin-bottom: 15px;
  display: block;
}

.upload-text {
  font-size: 16px;
  color: #495057;
  margin-bottom: 8px;
  font-weight: 500;
}

.upload-link {
  color: #667eea;
  text-decoration: underline;
  cursor: pointer;
}

.upload-link:hover {
  color: #5a6fd8;
}

.upload-hint {
  font-size: 12px;
  color: #8899a6;
  margin: 0;
}

/* File Preview Container */
.file-preview-container {
  margin-top: 20px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  background: white;
}

.file-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.file-count {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.btn-clear-all {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-clear-all:hover {
  background: rgba(220, 53, 69, 0.1);
}

.file-preview-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

/* File Preview Item */
.file-preview-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.3s ease;
}

.file-preview-item:last-child {
  margin-bottom: 0;
}

.file-preview-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.file-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: 6px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.file-thumbnail.image {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.file-thumbnail.video {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.file-thumbnail.audio {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.file-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 4px;
  word-break: break-word;
}

.file-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #8899a6;
}

.file-size {
  font-weight: 500;
}

.file-type {
  text-transform: uppercase;
}

.file-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.btn-file-action {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-remove {
  color: #dc3545;
}

.btn-remove:hover {
  background: rgba(220, 53, 69, 0.1);
}

.btn-preview {
  color: #667eea;
}

.btn-preview:hover {
  background: rgba(102, 126, 234, 0.1);
}

/* Upload Progress */
.upload-progress {
  margin-top: 8px;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.upload-progress-bar {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* File Type Icons */
.file-icon {
  font-size: 20px;
}

.file-icon.fa-image {
  color: #667eea;
}

.file-icon.fa-video {
  color: #ff6b6b;
}

.file-icon.fa-music {
  color: #feca57;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-zone {
    padding: 30px 15px;
  }

  .upload-icon {
    font-size: 2.5rem;
  }

  .upload-text {
    font-size: 14px;
  }

  .file-preview-item {
    padding: 10px;
  }

  .file-thumbnail {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .file-preview-header {
    padding: 10px 12px;
  }

  .file-details {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .upload-zone {
    padding: 20px 10px;
  }

  .file-preview-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .file-thumbnail {
    margin-right: 0;
  }

  .file-actions {
    justify-content: center;
  }
}

/* Template Attachments Display */
.template-attachments {
  margin: 8px 0;
  padding: 6px 10px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.template-attachments small {
  color: #495057;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.template-attachments i {
  color: #667eea;
  margin-right: 4px;
}

.template-attachments i[title] {
  cursor: help;
}

/* Existing file indicator */
.file-preview-item.existing {
  border-left: 3px solid #28a745;
}

.file-preview-item.existing .file-thumbnail::after {
  content: '✓';
  position: absolute;
  top: -2px;
  right: -2px;
  background: #28a745;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
